"use client";

import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";
import { Resume } from "@/types/resume";
import { useDisclosure } from "@heroui/react";
import React, { useEffect, useMemo, useState } from "react";
import { SidebarModal } from "../sidebar-modal";
import { ResumeEditSettings } from "./resume-edit-settings";
import { ResumePreviewControls } from "./resume-preview-controls";
import {
  ATSCompatibilityIndicator,
  TemplateRenderer,
} from "./templates/template-registry";
import { Template } from "@/types";

interface ResumePreviewProps {
  resume: Resume;
  className?: string;
  showTemplateSelector?: boolean;
  showControls?: boolean;
  templates: Template[];
}

export const ResumePreview: React.FC<ResumePreviewProps> = ({
  resume,
  className = "",
  showControls = true,
  templates,
}) => {
  const [scale, setScale] = useState<number>(1);

  // Template gallery modal state
  const {
    isOpen: isTemplateGalleryOpen,
    onOpen: onTemplateGalleryOpen,
    onOpenChange: onTemplateGalleryOpenChange,
  } = useDisclosure();

  // Customization context
  const {
    updateCustomization,
    colorScheme,
    fontFamily,
    spacing,
    margins,
    selectedTemplateId,
    setSelectedTemplateId,
  } = useResumeCustomization();

  const cssVariables = useMemo(
    () => ({
      "--resume-font-family": fontFamily?.family,
      "--resume-line-height": spacing?.value * 1.4,
      "--resume-background": colorScheme?.background,
      "--resume-text-color": colorScheme?.text,
      "--resume-primary-color": colorScheme?.primary,
      "--resume-spacing": `${spacing?.value * 2}px`,
      "--resume-margins": `${margins?.value * 2}px`,
    }),
    [fontFamily?.family, spacing?.value, colorScheme]
  );

  // Update template when resume template_id changes
  useEffect(() => {
    if (resume.template_id) {
      const templateExists = templates.find(
        (template) => template.id === resume.template_id
      );
      if (templateExists) {
        setSelectedTemplateId(resume.template_id);
      }
    }
  }, [resume.template_id, setSelectedTemplateId]);

  const currentTemplate = templates.find(
    (template) => template.id === selectedTemplateId
  );

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 1.5));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.3));
  };

  const handleResetZoom = () => {
    setScale(0.75);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log("Download PDF functionality to be implemented");
  };

  console.log({ selectedTemplateId, currentTemplate });

  return (
    <div
      className={`resume-preview flex flex-col h-full  ${className}`}
      style={cssVariables as React.CSSProperties}
    >
      {/* Controls */}
      {showControls && (
        <ResumePreviewControls
          onTemplateGalleryOpen={onTemplateGalleryOpen}
          scale={scale}
          handleZoomOut={handleZoomOut}
          handleZoomIn={handleZoomIn}
          handleResetZoom={handleResetZoom}
          handlePrint={handlePrint}
          handleDownloadPDF={handleDownloadPDF}
        />
      )}

      {/* Template Gallery Modal */}
      <SidebarModal
        isOpen={isTemplateGalleryOpen}
        onOpenChange={onTemplateGalleryOpenChange}
      >
        <ResumeEditSettings
          templates={templates}
          colorScheme={colorScheme}
          fontFamily={fontFamily}
          spacing={spacing}
          margins={margins}
          updateCustomization={updateCustomization}
        />
      </SidebarModal>

      {/* Preview Area */}
      <div className="preview-area flex-1 overflow-auto bg-gray-100 p-4">
        <div className="preview-container flex justify-center">
          <div
            className="resume-preview-content bg-white shadow-lg"
            style={{
              transform: `scale(${scale})`,
              transformOrigin: "top center",
              width: "8.5in",
              minHeight: "11in",
              margin: "0 auto",
            }}
          >
            <TemplateRenderer
              className="w-full h-full"
              resume={resume}
              templates={templates}
            />
          </div>
        </div>
      </div>

      {/* ATS Score Display */}
      {currentTemplate && (
        <div className="ats-score-display p-3 border-t bg-gray-50">
          <ATSCompatibilityIndicator
            score={currentTemplate.ats_score}
            showDetails={false}
          />
        </div>
      )}
    </div>
  );
};

export default ResumePreview;
