import { But<PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

interface ResumePreviewControlsProps {
  onTemplateGalleryOpen: () => void;
  scale: number;
  handleZoomOut: () => void;
  handleZoomIn: () => void;
  handleResetZoom: () => void;
  handlePrint: () => void;
  handleDownloadPDF: () => void;
}

export const ResumePreviewControls: React.FC<ResumePreviewControlsProps> = ({
  onTemplateGalleryOpen,
  scale,
  handleZoomOut,
  handleZoomIn,
  handleResetZoom,
  handlePrint,
  handleDownloadPDF,
}) => {
  return (
    <>
      <div className="preview-controls flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          {/* Template Customization Button */}
          <Tooltip
            content={
              <div className="p-2">
                <p className="font-semibold mb-1">Customize Your Resume</p>
                <p className="text-sm text-gray-600">
                  Change templates, colors, fonts, and layout options
                </p>
              </div>
            }
          >
            <Button
              className="font-medium shadow-lg hover:shadow-xl transition-all duration-200 animate-pulse hover:animate-none"
              color="primary"
              size="sm"
              startContent={<Icon icon="lucide:palette" />}
              variant="flat"
              onPress={onTemplateGalleryOpen}
            >
              Customize
            </Button>
          </Tooltip>
        </div>

        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <div className="flex items-center gap-1 border rounded-lg p-1">
            <Button
              isIconOnly
              isDisabled={scale <= 0.3}
              size="sm"
              variant="light"
              onPress={handleZoomOut}
            >
              <Icon icon="lucide:zoom-out" />
            </Button>
            <span className="text-sm px-2 min-w-[60px] text-center">
              {Math.round(scale * 100)}%
            </span>
            <Button
              isIconOnly
              isDisabled={scale >= 1.5}
              size="sm"
              variant="light"
              onPress={handleZoomIn}
            >
              <Icon icon="lucide:zoom-in" />
            </Button>
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={handleResetZoom}
            >
              <Icon icon="lucide:maximize" />
            </Button>
          </div>

          {/* Action Buttons */}
          <Button
            size="sm"
            startContent={<Icon icon="lucide:printer" />}
            variant="light"
            onPress={handlePrint}
          >
            Print
          </Button>
          <Button
            color="primary"
            size="sm"
            startContent={<Icon icon="lucide:download" />}
            onPress={handleDownloadPDF}
          >
            PDF
          </Button>
        </div>
      </div>
    </>
  );
};
