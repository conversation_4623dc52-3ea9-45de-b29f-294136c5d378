import React from "react";
import {
  Resume,
  ExperienceType,
  EducationType,
  SkillType,
} from "@/types/resume";

// Base interfaces for template props
export interface TemplateProps {
  resume: Resume;
  className?: string;
}

// Utility functions for ATS compatibility
export const formatDate = (
  dateString: string,
  showPresent: boolean = false,
): string => {
  if (!dateString && showPresent) return "Present";
  if (!dateString) return "";

  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
  });
};

export const formatDateRange = (
  startDate: string,
  endDate: string,
  isCurrent: boolean = false,
): string => {
  const start = formatDate(startDate);
  const end = isCurrent ? "Present" : formatDate(endDate);
  return `${start} - ${end}`;
};

export const getFullName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`.trim();
};

export const formatLocation = (city: string, country: string): string => {
  const parts = [city, country].filter(Boolean);
  return parts.join(", ");
};

// Base Header Component - ATS friendly with proper hierarchy
export const ResumeHeader: React.FC<{
  resume: Resume;
  showPhoto?: boolean;
  headerStyle?: "centered" | "left" | "split";
}> = ({ resume, showPhoto = true, headerStyle = "centered" }) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  const contactInfo = [
    resume.email,
    resume.website,
    location,
    resume.address,
  ].filter(Boolean);

  return (
    <header
      className={`resume-header ${headerStyle === "centered" ? "text-center" : headerStyle === "split" ? "flex justify-between items-start" : "text-left"} mb-6`}
    >
      <div className={headerStyle === "split" ? "flex-1" : ""}>
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
          {fullName}
        </h1>
        {resume.job_title && (
          <h2 className="text-lg md:text-xl text-gray-700 mb-3 font-medium">
            {resume.job_title}
          </h2>
        )}
        <div
          className={`contact-info ${headerStyle === "centered" ? "flex flex-wrap justify-center gap-x-4 gap-y-1" : "flex flex-wrap gap-x-4 gap-y-1"} text-sm text-gray-600`}
        >
          {contactInfo.map((info, index) => (
            <span key={index} className="whitespace-nowrap">
              {info}
            </span>
          ))}
        </div>
      </div>

      {showPhoto &&
        resume.show_photo &&
        resume.photo &&
        headerStyle === "split" && (
          <div className="ml-6 flex-shrink-0">
            <img
              alt={`${fullName} profile photo`}
              className="w-24 h-24 rounded-lg object-cover border border-gray-200"
              src={resume.photo}
            />
          </div>
        )}
    </header>
  );
};

// Base Section Component - ATS optimized
export const ResumeSection: React.FC<{
  title: string;
  children: React.ReactNode;
  className?: string;
}> = ({ title, children, className = "" }) => {
  return (
    <section className={`resume-section mb-6 ${className}`}>
      <h2 className="text-lg font-semibold text-gray-900 mb-3 pb-1 border-b border-gray-300 uppercase tracking-wide">
        {title}
      </h2>
      <div className="section-content">{children}</div>
    </section>
  );
};

// Professional Summary Component
export const ProfessionalSummary: React.FC<{ bio: string }> = ({ bio }) => {
  if (!bio) return null;

  return (
    <ResumeSection title="Professional Summary">
      <div
        dangerouslySetInnerHTML={{ __html: bio }}
        className="leading-relaxed text-sm prose prose-sm max-w-none"
      />
    </ResumeSection>
  );
};

// Experience Component - ATS optimized
export const ExperienceSection: React.FC<{ experiences: ExperienceType[] }> = ({
  experiences,
}) => {
  if (!experiences?.length) return null;

  return (
    <div className="space-y-4">
      {experiences.map((exp, index) => (
        <div key={exp.id || index} className="experience-item">
          <div className="flex justify-between items-start mb-1">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm">
                {exp.title}
              </h3>
              <p className="text-gray-700 text-sm font-medium">{exp.company}</p>
            </div>
            <div className="text-right text-sm text-gray-600 ml-4">
              <p>
                {formatDateRange(exp.start_date, exp.end_date, exp.is_current)}
              </p>
              {formatLocation(exp.city, exp.country) && (
                <p className="text-xs">
                  {formatLocation(exp.city, exp.country)}
                </p>
              )}
            </div>
          </div>
          {exp.description && (
            <div className="text-sm text-gray-700 leading-relaxed mt-2">
              {exp.description.split("\n").map((line, i) => (
                <p key={i} className="mb-1">
                  {line}
                </p>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Education Component - ATS optimized
export const EducationSection: React.FC<{ educations: EducationType[] }> = ({
  educations,
}) => {
  if (!educations?.length) return null;

  return (
    <div className="space-y-3">
      {educations.map((edu, index) => (
        <div key={edu.id || index} className="education-item">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm">
                {edu.degree} {edu.field_of_study && `in ${edu.field_of_study}`}
              </h3>
              <p className="text-gray-700 text-sm">{edu.institution}</p>
            </div>
            <div className="text-right text-sm text-gray-600 ml-4">
              <p>
                {formatDateRange(edu.start_date, edu.end_date, edu.is_current)}
              </p>
              {formatLocation(edu.city, edu.country) && (
                <p className="text-xs">
                  {formatLocation(edu.city, edu.country)}
                </p>
              )}
            </div>
          </div>
          {edu.description && (
            <p className="text-sm text-gray-700 mt-1 leading-relaxed">
              {edu.description}
            </p>
          )}
        </div>
      ))}
    </div>
  );
};

// Skills Component - ATS optimized with categories
export const SkillsSection: React.FC<{ skills: SkillType[] }> = ({
  skills,
}) => {
  if (!skills?.length) return null;

  // Group skills by category
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      const category = skill.category || "General";
      if (!acc[category]) acc[category] = [];
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, SkillType[]>,
  );

  return (
    <div className="space-y-2">
      {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
        <div key={category} className="skill-category">
          <span className="font-medium text-gray-900 text-sm">{category}:</span>
          <span className="ml-2 text-gray-700 text-sm">
            {categorySkills.map((skill) => skill.name).join(", ")}
          </span>
        </div>
      ))}
    </div>
  );
};
