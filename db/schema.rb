# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_19_222837) do
  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "awards", force: :cascade do |t|
    t.string "title"
    t.string "issuer"
    t.string "url"
    t.datetime "date_received"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_awards_on_resume_id"
  end

  create_table "certifications", force: :cascade do |t|
    t.string "title"
    t.string "issuer"
    t.string "url"
    t.datetime "date_received"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_certifications_on_resume_id"
  end

  create_table "educations", force: :cascade do |t|
    t.string "city"
    t.string "country"
    t.string "institution"
    t.boolean "is_current"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text "description"
    t.string "degree"
    t.string "field_of_study"
    t.string "website"
    t.integer "sort"
    t.integer "resume_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_educations_on_resume_id"
  end

  create_table "experiences", force: :cascade do |t|
    t.integer "resume_id", null: false
    t.string "title"
    t.string "company"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text "description"
    t.string "city"
    t.string "country"
    t.boolean "is_current", default: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_experiences_on_resume_id"
  end

  create_table "hobbies", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_hobbies_on_resume_id"
  end

  create_table "languages", force: :cascade do |t|
    t.string "name"
    t.integer "proficiency"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_languages_on_resume_id"
  end

  create_table "projects", force: :cascade do |t|
    t.string "title"
    t.string "client"
    t.string "url"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_projects_on_resume_id"
  end

  create_table "references", force: :cascade do |t|
    t.string "name"
    t.string "company"
    t.string "position"
    t.string "email"
    t.string "phone"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_references_on_resume_id"
  end

  create_table "resumes", force: :cascade do |t|
    t.string "title"
    t.string "first_name"
    t.string "last_name"
    t.string "job_title"
    t.string "address"
    t.string "email"
    t.string "website"
    t.text "bio"
    t.datetime "birth_date"
    t.string "city"
    t.string "street"
    t.string "country"
    t.boolean "show_photo"
    t.integer "user_id", null: false
    t.integer "template_id", default: 1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_resumes_on_user_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.string "token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "skills", force: :cascade do |t|
    t.string "name"
    t.integer "proficiency"
    t.string "category"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_skills_on_resume_id"
  end

  create_table "templates", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.string "ats_score"
    t.string "category"
    t.text "features"
    t.text "description"
    t.string "image"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
  end

  create_table "volunteerings", force: :cascade do |t|
    t.string "organization"
    t.string "role"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text "description"
    t.integer "resume_id", null: false
    t.integer "sort"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resume_id"], name: "index_volunteerings_on_resume_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "awards", "resumes"
  add_foreign_key "certifications", "resumes"
  add_foreign_key "educations", "resumes"
  add_foreign_key "experiences", "resumes"
  add_foreign_key "hobbies", "resumes"
  add_foreign_key "languages", "resumes"
  add_foreign_key "projects", "resumes"
  add_foreign_key "references", "resumes"
  add_foreign_key "resumes", "users"
  add_foreign_key "sessions", "users"
  add_foreign_key "skills", "resumes"
  add_foreign_key "volunteerings", "resumes"
end
